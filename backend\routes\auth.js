const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const rateLimit = require('express-rate-limit');
const User = require('../models/User');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const blockchainService = require('../utils/blockchain');
const memoryStore = require('../utils/memoryStore');

const router = express.Router();

// Check if database is available
const isDatabaseAvailable = () => {
  const mongoose = require('mongoose');
  return mongoose.connection.readyState === 1;
};

// Rate limiting for auth routes
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later'
  }
});

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE || '7d'
  });
};

// @route   POST /api/auth/create-admin
// @desc    Create initial admin user (SECURE - requires admin key or first-time setup)
// @access  Protected (Admin key required or first-time setup only)
router.post('/create-admin', async (req, res) => {
  try {
    // Check if any admin user already exists
    const existingAdmin = await User.findOne({ role: 'admin', isActive: true });

    if (existingAdmin) {
      return res.status(400).json({
        success: false,
        message: 'Admin user already exists. Use admin panel to create additional users.'
      });
    }

    // Require admin setup key for security
    const { adminSetupKey, username, email, password, firstName, lastName } = req.body;

    // Check for admin setup key (should be set in environment)
    const requiredSetupKey = process.env.ADMIN_SETUP_KEY || 'CHANGE_ME_IN_PRODUCTION';

    if (!adminSetupKey || adminSetupKey !== requiredSetupKey) {
      return res.status(403).json({
        success: false,
        message: 'Invalid admin setup key. Contact system administrator.'
      });
    }

    // Validate required fields
    if (!username || !email || !password || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields: username, email, password, firstName, lastName'
      });
    }

    // Password strength validation
    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }

    // Create the first admin user with provided data
    const adminUser = new User({
      username,
      email,
      password,
      firstName,
      lastName,
      role: 'admin',
      department: 'Administration',
      isActive: true
    });

    await adminUser.save();

    res.status(201).json({
      success: true,
      message: 'Admin user created successfully. Please login with your credentials.',
      user: {
        username: adminUser.username,
        email: adminUser.email,
        role: adminUser.role,
        fullName: adminUser.fullName
      }
    });
  } catch (error) {
    console.error('Create admin error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/auth/register
// @desc    Register a new user (admin only)
// @access  Private (Admin)
router.post('/register', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      username,
      email,
      password,
      role,
      firstName,
      lastName,
      badgeNumber,
      barNumber,
      department,
      walletAddress
    } = req.body;

    // Validation
    if (!username || !email || !password || !role || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }

    // Role-specific validation
    if (role === 'police' && !badgeNumber) {
      return res.status(400).json({
        success: false,
        message: 'Badge number is required for police officers'
      });
    }

    if (role === 'lawyer' && !barNumber) {
      return res.status(400).json({
        success: false,
        message: 'Bar number is required for lawyers'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email or username already exists'
      });
    }

    // Create new user
    const user = new User({
      username,
      email,
      password,
      role,
      firstName,
      lastName,
      badgeNumber,
      barNumber,
      department,
      walletAddress
    });

    await user.save();

    // Grant blockchain role if wallet address is provided
    if (walletAddress && blockchainService.isConnected()) {
      const adminAddress = blockchainService.getAccounts()[0];
      
      if (role === 'police') {
        await blockchainService.grantPoliceRole(walletAddress, adminAddress);
      } else if (role === 'lawyer') {
        await blockchainService.grantLawyerRole(walletAddress, adminAddress);
      }
    }

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        fullName: user.fullName
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error during registration'
    });
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', async (req, res) => {
  try {
    console.log('🔍 Login attempt received');
    console.log('Request body:', req.body);

    const { username, password } = req.body;

    if (!username || !password) {
      console.log('❌ Missing username or password');
      return res.status(400).json({
        success: false,
        message: 'Please provide username and password'
      });
    }

    console.log('🔍 Looking for user:', username);

    let user;
    if (isDatabaseAvailable()) {
      // Use MongoDB
      user = await User.findOne({
        $or: [{ username }, { email: username }],
        isActive: true
      });
    } else {
      // Use memory store
      console.log('📝 Using memory store (database not available)');
      user = await memoryStore.findUser(username);
    }

    console.log('🔍 User found:', user ? user.username : 'No user found');

    if (!user) {
      console.log('❌ User not found');
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check password
    console.log('🔍 Checking password for user:', user.username);
    let isPasswordValid;
    if (isDatabaseAvailable() && user.comparePassword) {
      // Use MongoDB user method
      isPasswordValid = await user.comparePassword(password);
    } else {
      // Use memory store comparison
      isPasswordValid = await memoryStore.comparePassword(user, password);
    }
    console.log('🔍 Password valid:', isPasswordValid);

    if (!isPasswordValid) {
      console.log('❌ Invalid password');
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate token
    const token = generateToken(user._id);

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        role: user.role,
        fullName: user.fullName,
        walletAddress: user.walletAddress
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during login'
    });
  }
});

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    
    res.json({
      success: true,
      user
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const { firstName, lastName, department, walletAddress } = req.body;
    
    const updateFields = {};
    if (firstName) updateFields.firstName = firstName;
    if (lastName) updateFields.lastName = lastName;
    if (department) updateFields.department = department;
    if (walletAddress) updateFields.walletAddress = walletAddress;

    const user = await User.findByIdAndUpdate(
      req.user.id,
      updateFields,
      { new: true, runValidators: true }
    ).select('-password');

    res.json({
      success: true,
      message: 'Profile updated successfully',
      user
    });
  } catch (error) {
    console.error('Profile update error:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error during profile update'
    });
  }
});

// @route   POST /api/auth/change-password
// @desc    Change user password
// @access  Private
router.post('/change-password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Please provide current and new password'
      });
    }

    const user = await User.findById(req.user.id);
    
    // Verify current password
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Password change error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during password change'
    });
  }
});

module.exports = router;
