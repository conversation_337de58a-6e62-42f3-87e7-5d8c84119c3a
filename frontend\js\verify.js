// Evidence Verification Management
class VerificationManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupVerificationForm();
    }

    setupVerificationForm() {
        const verifyBtn = document.getElementById('verify-btn');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', () => this.handleVerification());
        }

        const evidenceIdInput = document.getElementById('verify-evidence-id');
        if (evidenceIdInput) {
            evidenceIdInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleVerification();
                }
            });
        }
    }

    async handleVerification() {
        const evidenceIdInput = document.getElementById('verify-evidence-id');
        const evidenceId = evidenceIdInput.value.trim();

        if (!evidenceId) {
            showToast('Please enter an evidence ID', 'error');
            return;
        }

        if (!window.authManager || !window.authManager.canVerifyEvidence()) {
            showToast('You do not have permission to verify evidence', 'error');
            return;
        }

        try {
            showLoading();
            
            // First get the evidence details
            const evidenceResponse = await api.getEvidence(evidenceId);
            
            if (!evidenceResponse.success) {
                showToast('Evidence not found', 'error');
                return;
            }

            const evidence = evidenceResponse.evidence;
            
            // Then verify the evidence
            const verificationResponse = await api.verifyEvidence(evidenceId);
            
            if (verificationResponse.success) {
                this.showVerificationResult(evidence, verificationResponse.verification);
            } else {
                showToast('Verification failed', 'error');
            }
        } catch (error) {
            console.error('Verification error:', error);
            showToast('Error during verification. Please try again.', 'error');
        } finally {
            hideLoading();
        }
    }

    showVerificationResult(evidence, verification) {
        const resultDiv = document.getElementById('verification-result');
        if (!resultDiv) return;

        const isValid = verification.isValid;
        const statusClass = isValid ? 'success' : 'danger';
        const statusIcon = isValid ? 'fas fa-check-circle' : 'fas fa-times-circle';
        const statusText = isValid ? 'VERIFIED' : 'VERIFICATION FAILED';

        const resultHTML = `
            <div class="verification-card">
                <div class="verification-header ${statusClass}">
                    <i class="${statusIcon}"></i>
                    <h2>${statusText}</h2>
                </div>
                
                <div class="verification-details">
                    <div class="evidence-summary">
                        <h3>Evidence Information</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <strong>Evidence ID:</strong>
                                <span>${evidence.evidenceId}</span>
                            </div>
                            <div class="info-item">
                                <strong>File Name:</strong>
                                <span>${escapeHtml(evidence.originalName)}</span>
                            </div>
                            <div class="info-item">
                                <strong>Case ID:</strong>
                                <span>${escapeHtml(evidence.caseId)}</span>
                            </div>
                            <div class="info-item">
                                <strong>Upload Date:</strong>
                                <span>${formatDate(evidence.createdAt)}</span>
                            </div>
                            <div class="info-item">
                                <strong>Uploaded By:</strong>
                                <span>${evidence.uploader?.fullName || evidence.uploader?.username || 'Unknown'}</span>
                            </div>
                            <div class="info-item">
                                <strong>File Size:</strong>
                                <span>${formatFileSize(evidence.fileSize)}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="verification-checks">
                        <h3>Verification Checks</h3>
                        <div class="check-list">
                            <div class="check-item ${verification.fileIntegrityValid ? 'success' : 'danger'}">
                                <i class="${verification.fileIntegrityValid ? 'fas fa-check' : 'fas fa-times'}"></i>
                                <span>File Integrity Check</span>
                                <small>${verification.fileIntegrityValid ? 'File hash matches original' : 'File hash does not match'}</small>
                            </div>
                            
                            ${verification.blockchainValid !== undefined ? `
                                <div class="check-item ${verification.blockchainValid ? 'success' : 'danger'}">
                                    <i class="${verification.blockchainValid ? 'fas fa-check' : 'fas fa-times'}"></i>
                                    <span>Blockchain Verification</span>
                                    <small>${verification.blockchainValid ? 'Evidence exists on blockchain' : 'Evidence not found on blockchain'}</small>
                                </div>
                            ` : `
                                <div class="check-item warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>Blockchain Verification</span>
                                    <small>Blockchain not available</small>
                                </div>
                            `}
                        </div>
                    </div>
                    
                    <div class="verification-metadata">
                        <h3>Verification Details</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <strong>Verified At:</strong>
                                <span>${formatDate(verification.verifiedAt)}</span>
                            </div>
                            <div class="info-item">
                                <strong>Verified By:</strong>
                                <span>${verification.verifiedBy.fullName || verification.verifiedBy.username}</span>
                            </div>
                            <div class="info-item">
                                <strong>File Hash:</strong>
                                <span class="hash-display" title="${evidence.fileHash}">
                                    ${evidence.fileHash}
                                    <button class="btn-copy" onclick="copyToClipboard('${evidence.fileHash}')" title="Copy hash">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </span>
                            </div>
                            ${evidence.blockchainTxHash ? `
                                <div class="info-item">
                                    <strong>Blockchain TX:</strong>
                                    <span class="hash-display" title="${evidence.blockchainTxHash}">
                                        ${evidence.blockchainTxHash}
                                        <button class="btn-copy" onclick="copyToClipboard('${evidence.blockchainTxHash}')" title="Copy transaction hash">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                    
                    ${evidence.description ? `
                        <div class="evidence-description">
                            <h3>Description</h3>
                            <p>${escapeHtml(evidence.description)}</p>
                        </div>
                    ` : ''}
                    
                    <div class="verification-actions">
                        <button class="btn btn-primary" onclick="evidenceManager.viewEvidence('${evidence._id}')">
                            <i class="fas fa-eye"></i> View Full Details
                        </button>
                        <button class="btn btn-secondary" onclick="evidenceManager.downloadEvidence('${evidence._id}', '${escapeHtml(evidence.originalName)}')">
                            <i class="fas fa-download"></i> Download Evidence
                        </button>
                        <button class="btn btn-info" onclick="verificationManager.generateReport('${evidence._id}')">
                            <i class="fas fa-file-alt"></i> Generate Report
                        </button>
                    </div>
                </div>
            </div>
        `;

        resultDiv.innerHTML = resultHTML;
        resultDiv.style.display = 'block';

        // Scroll to result
        resultDiv.scrollIntoView({ behavior: 'smooth' });

        // Show toast notification
        const message = isValid ? 
            'Evidence verification completed successfully' : 
            'Evidence verification failed - integrity compromised';
        const toastType = isValid ? 'success' : 'error';
        
        showToast(message, toastType);
    }

    async generateReport(evidenceId) {
        try {
            showLoading();
            
            // Get evidence details
            const response = await api.getEvidence(evidenceId);
            
            if (response.success) {
                const evidence = response.evidence;
                this.downloadVerificationReport(evidence);
            } else {
                showToast('Failed to generate report', 'error');
            }
        } catch (error) {
            console.error('Report generation error:', error);
            showToast('Error generating report', 'error');
        } finally {
            hideLoading();
        }
    }

    downloadVerificationReport(evidence) {
        const reportContent = this.generateReportContent(evidence);
        const blob = new Blob([reportContent], { type: 'text/plain' });
        const fileName = `verification_report_${evidence.evidenceId}_${new Date().toISOString().split('T')[0]}.txt`;
        
        downloadBlob(blob, fileName);
        showToast('Verification report downloaded', 'success');
    }

    generateReportContent(evidence) {
        const currentUser = window.authManager ? window.authManager.getCurrentUser() : { username: 'Unknown', role: 'unknown' };
        const timestamp = new Date().toISOString();
        
        return `
EVIDENCE VERIFICATION REPORT
============================

Report Generated: ${formatDate(timestamp)}
Generated By: ${currentUser?.fullName || currentUser?.username || 'Unknown'}
Report ID: ${generateId()}

EVIDENCE INFORMATION
-------------------
Evidence ID: ${evidence.evidenceId}
File Name: ${evidence.originalName}
Case ID: ${evidence.caseId}
File Size: ${formatFileSize(evidence.fileSize)}
MIME Type: ${evidence.mimeType}
Upload Date: ${formatDate(evidence.createdAt)}
Uploaded By: ${evidence.uploader?.fullName || evidence.uploader?.username || 'Unknown'}

VERIFICATION DETAILS
-------------------
File Hash (SHA-256): ${evidence.fileHash}
Verification Status: ${evidence.isVerified ? 'VERIFIED' : 'UNVERIFIED'}
${evidence.blockchainTxHash ? `Blockchain Transaction: ${evidence.blockchainTxHash}` : 'Blockchain Transaction: Not Available'}
${evidence.blockNumber ? `Block Number: ${evidence.blockNumber}` : ''}

DESCRIPTION
-----------
${evidence.description}

VERIFICATION HISTORY
-------------------
${evidence.verificationAttempts && evidence.verificationAttempts.length > 0 ? 
    evidence.verificationAttempts.map(attempt => 
        `${formatDate(attempt.timestamp)} - ${attempt.verifier?.fullName || 'Unknown'} - ${attempt.result ? 'PASSED' : 'FAILED'}`
    ).join('\n') : 
    'No verification attempts recorded'
}

ACCESS LOG
----------
${evidence.accessLog && evidence.accessLog.length > 0 ? 
    evidence.accessLog.slice(-10).map(log => 
        `${formatDate(log.timestamp)} - ${log.accessor?.fullName || 'Unknown'} - ${log.action.toUpperCase()}`
    ).join('\n') : 
    'No access log available'
}

DIGITAL SIGNATURE
-----------------
This report was generated by the Evidence Protection System.
Report Hash: ${this.generateReportHash(evidence, timestamp)}

WARNING: This report is for official use only. Any unauthorized access, 
modification, or distribution is strictly prohibited and may be subject 
to legal action.
        `.trim();
    }

    generateReportHash(evidence, timestamp) {
        // Simple hash generation for demonstration
        const data = `${evidence.evidenceId}${evidence.fileHash}${timestamp}`;
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16).padStart(8, '0');
    }

    clearResults() {
        const resultDiv = document.getElementById('verification-result');
        if (resultDiv) {
            resultDiv.style.display = 'none';
            resultDiv.innerHTML = '';
        }
        
        const evidenceIdInput = document.getElementById('verify-evidence-id');
        if (evidenceIdInput) {
            evidenceIdInput.value = '';
        }
    }
}

// Create global verification manager instance
const verificationManager = new VerificationManager();
