const express = require('express');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Evidence = require('../models/Evidence');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const blockchainService = require('../utils/blockchain');
const archiver = require('archiver');
const fs = require('fs');
const path = require('path');

const router = express.Router();

// @route   GET /api/admin/users
// @desc    Get all users (admin only)
// @access  Private (Admin)
router.get('/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', role = '' } = req.query;
    
    // Build search query
    const query = { isActive: true };
    
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { fullName: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (role) {
      query.role = role;
    }
    
    // Get users with pagination
    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);
    
    const total = await User.countDocuments(query);
    
    res.json({
      success: true,
      users,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/admin/users
// @desc    Create new user (admin only)
// @access  Private (Admin)
router.post('/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { username, email, password, fullName, role, department } = req.body;
    
    // Validation
    if (!username || !email || !password || !fullName || !role) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ username }, { email }]
    });
    
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this username or email already exists'
      });
    }
    
    // Create new user
    const user = new User({
      username,
      email,
      password,
      fullName,
      role,
      department: department || 'General',
      isActive: true,
      createdBy: req.user._id
    });
    
    await user.save();
    
    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;
    
    res.status(201).json({
      success: true,
      message: 'User created successfully',
      user: userResponse
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/admin/users/:id
// @desc    Update user (admin only)
// @access  Private (Admin)
router.put('/users/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, fullName, role, department, isActive } = req.body;
    
    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Prevent admin from deactivating themselves
    if (user._id.toString() === req.user._id.toString() && isActive === false) {
      return res.status(400).json({
        success: false,
        message: 'You cannot deactivate your own account'
      });
    }
    
    // Update user fields
    if (username) user.username = username;
    if (email) user.email = email;
    if (fullName) user.fullName = fullName;
    if (role) user.role = role;
    if (department) user.department = department;
    if (typeof isActive === 'boolean') user.isActive = isActive;
    
    user.updatedAt = new Date();
    user.updatedBy = req.user._id;
    
    await user.save();
    
    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;
    
    res.json({
      success: true,
      message: 'User updated successfully',
      user: userResponse
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/admin/users/:id
// @desc    Delete user (admin only)
// @access  Private (Admin)
router.delete('/users/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Prevent admin from deleting themselves
    if (user._id.toString() === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'You cannot delete your own account'
      });
    }
    
    // Soft delete - deactivate instead of removing
    user.isActive = false;
    user.deletedAt = new Date();
    user.deletedBy = req.user._id;
    
    await user.save();
    
    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/admin/users/:id/reset-password
// @desc    Reset user password (admin only)
// @access  Private (Admin)
router.post('/users/:id/reset-password', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;
    
    if (!newPassword || newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters long'
      });
    }
    
    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Update password
    user.password = newPassword;
    user.updatedAt = new Date();
    user.updatedBy = req.user._id;
    
    await user.save();
    
    res.json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/stats
// @desc    Get system statistics (admin only)
// @access  Private (Admin)
router.get('/stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Get user statistics
    const totalUsers = await User.countDocuments({ isActive: true });
    const usersByRole = await User.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);
    
    // Get evidence statistics
    const totalEvidence = await Evidence.countDocuments();
    const verifiedEvidence = await Evidence.countDocuments({ isVerified: true });
    const evidenceByMonth = await Evidence.aggregate([
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } },
      { $limit: 12 }
    ]);
    
    // Recent activity
    const recentUsers = await User.find({ isActive: true })
      .select('-password')
      .sort({ createdAt: -1 })
      .limit(5);
    
    const recentEvidence = await Evidence.find()
      .populate('uploadedBy', 'username fullName')
      .sort({ createdAt: -1 })
      .limit(5);
    
    res.json({
      success: true,
      stats: {
        users: {
          total: totalUsers,
          byRole: usersByRole,
          recent: recentUsers
        },
        evidence: {
          total: totalEvidence,
          verified: verifiedEvidence,
          verificationRate: totalEvidence > 0 ? ((verifiedEvidence / totalEvidence) * 100).toFixed(1) : 0,
          byMonth: evidenceByMonth,
          recent: recentEvidence
        }
      }
    });
  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/legal-export/:caseId
// @desc    Generate legal export bundle with blockchain proof
// @access  Private (Admin only)
router.get('/legal-export/:caseId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { caseId } = req.params;

    // Get all evidence for the case
    const evidences = await Evidence.find({
      caseId: caseId,
      isActive: true
    })
      .populate('uploader', 'username firstName lastName role')
      .populate('accessLog.accessor', 'username firstName lastName role')
      .populate('verificationAttempts.verifier', 'username firstName lastName role');

    if (evidences.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No evidence found for this case'
      });
    }

    // Create ZIP archive
    const archive = archiver('zip', { zlib: { level: 9 } });

    res.attachment(`case-${caseId}-legal-export.zip`);
    archive.pipe(res);

    // Add evidence files and metadata
    for (const evidence of evidences) {
      // Add the actual file
      if (fs.existsSync(evidence.filePath)) {
        archive.file(evidence.filePath, { name: `evidence/${evidence.originalName}` });
      }

      // Create metadata file
      const metadata = {
        evidenceId: evidence.evidenceId,
        fileName: evidence.originalName,
        fileHash: evidence.fileHash,
        uploadedAt: evidence.uploadedAt,
        uploader: evidence.uploader,
        description: evidence.description,
        caseId: evidence.caseId,
        blockchainTxHash: evidence.blockchainTxHash,
        blockNumber: evidence.blockNumber,
        isVerified: evidence.isVerified,
        verificationAttempts: evidence.verificationAttempts,
        accessLog: evidence.accessLog,
        tags: evidence.tags
      };

      archive.append(JSON.stringify(metadata, null, 2), {
        name: `metadata/${evidence.evidenceId}-metadata.json`
      });

      // Create chain of custody report
      const custodyReport = generateChainOfCustodyReport(evidence);
      archive.append(custodyReport, {
        name: `custody/${evidence.evidenceId}-custody.txt`
      });
    }

    // Add case summary
    const caseSummary = generateCaseSummary(caseId, evidences);
    archive.append(caseSummary, { name: 'case-summary.txt' });

    archive.finalize();

  } catch (error) {
    console.error('Legal export error:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating legal export'
    });
  }
});

// @route   GET /api/admin/blockchain-health
// @desc    Get blockchain health monitor data
// @access  Private (Admin only)
router.get('/blockchain-health', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const healthData = await getBlockchainHealthData();
    res.json({
      success: true,
      health: healthData
    });
  } catch (error) {
    console.error('Blockchain health error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching blockchain health data'
    });
  }
});

// @route   GET /api/admin/real-time-logs
// @desc    Get real-time system logs and timestamps
// @access  Private (Admin only)
router.get('/real-time-logs', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const logs = await getRealTimeLogs();
    res.json({
      success: true,
      logs: logs
    });
  } catch (error) {
    console.error('Real-time logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching real-time logs'
    });
  }
});

// Helper function to generate chain of custody report
function generateChainOfCustodyReport(evidence) {
  let report = `CHAIN OF CUSTODY REPORT\n`;
  report += `========================\n\n`;
  report += `Evidence ID: ${evidence.evidenceId}\n`;
  report += `File Name: ${evidence.originalName}\n`;
  report += `File Hash: ${evidence.fileHash}\n`;
  report += `Case ID: ${evidence.caseId}\n`;
  report += `Upload Date: ${evidence.uploadedAt}\n`;
  report += `Uploader: ${evidence.uploader?.firstName} ${evidence.uploader?.lastName} (${evidence.uploader?.username})\n`;
  report += `Blockchain TX: ${evidence.blockchainTxHash || 'N/A'}\n`;
  report += `Block Number: ${evidence.blockNumber || 'N/A'}\n\n`;

  report += `ACCESS LOG\n`;
  report += `----------\n`;
  if (evidence.accessLog && evidence.accessLog.length > 0) {
    evidence.accessLog.forEach(log => {
      report += `${log.timestamp} - ${log.accessor?.username || 'Unknown'} - ${log.action} - IP: ${log.ipAddress}\n`;
    });
  } else {
    report += `No access log entries\n`;
  }

  report += `\nVERIFICATION HISTORY\n`;
  report += `-------------------\n`;
  if (evidence.verificationAttempts && evidence.verificationAttempts.length > 0) {
    evidence.verificationAttempts.forEach(attempt => {
      report += `${attempt.timestamp} - ${attempt.verifier?.username || 'Unknown'} - ${attempt.result ? 'PASSED' : 'FAILED'}\n`;
    });
  } else {
    report += `No verification attempts\n`;
  }

  return report;
}

// Helper function to generate case summary
function generateCaseSummary(caseId, evidences) {
  let summary = `CASE SUMMARY REPORT\n`;
  summary += `==================\n\n`;
  summary += `Case ID: ${caseId}\n`;
  summary += `Total Evidence Items: ${evidences.length}\n`;
  summary += `Verified Items: ${evidences.filter(e => e.isVerified).length}\n`;
  summary += `Unverified Items: ${evidences.filter(e => !e.isVerified).length}\n`;
  summary += `Export Generated: ${new Date().toISOString()}\n\n`;

  summary += `EVIDENCE INVENTORY\n`;
  summary += `-----------------\n`;
  evidences.forEach(evidence => {
    summary += `${evidence.evidenceId} - ${evidence.originalName} - ${evidence.isVerified ? 'VERIFIED' : 'UNVERIFIED'}\n`;
  });

  return summary;
}

// Helper function to get blockchain health data
async function getBlockchainHealthData() {
  try {
    const isConnected = blockchainService.isConnected();

    if (!isConnected) {
      return {
        status: 'Disconnected',
        contractStatus: 'Inactive',
        avgGasUsed: 0,
        eventsEmitted: 0,
        rolesAssigned: { uploader: 0, admin: 0 },
        network: 'Unknown'
      };
    }

    // Get blockchain stats
    const accounts = blockchainService.getAccounts();
    const totalEvidences = await blockchainService.getTotalEvidences();

    // Get recent evidence for gas calculation
    const recentEvidences = await Evidence.find({
      gasUsed: { $exists: true, $ne: null }
    }).limit(10);

    const avgGasUsed = recentEvidences.length > 0
      ? Math.round(recentEvidences.reduce((sum, e) => sum + (e.gasUsed || 0), 0) / recentEvidences.length)
      : 0;

    // Count roles
    const uploaderCount = await User.countDocuments({ role: 'Police' });
    const adminCount = await User.countDocuments({ role: 'Admin' });

    return {
      status: 'Connected',
      contractStatus: 'Active',
      avgGasUsed: avgGasUsed,
      eventsEmitted: totalEvidences.success ? totalEvidences.total : 0,
      rolesAssigned: {
        uploader: uploaderCount,
        admin: adminCount
      },
      network: process.env.BLOCKCHAIN_NETWORK || 'development',
      accounts: accounts.length,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Blockchain health data error:', error);
    return {
      status: 'Error',
      contractStatus: 'Unknown',
      avgGasUsed: 0,
      eventsEmitted: 0,
      rolesAssigned: { uploader: 0, admin: 0 },
      network: 'Unknown',
      error: error.message
    };
  }
}

// Helper function to get real-time logs
async function getRealTimeLogs() {
  try {
    // Get recent access logs from evidence
    const recentAccess = await Evidence.aggregate([
      { $unwind: '$accessLog' },
      { $sort: { 'accessLog.timestamp': -1 } },
      { $limit: 50 },
      {
        $lookup: {
          from: 'users',
          localField: 'accessLog.accessor',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $project: {
          timestamp: '$accessLog.timestamp',
          action: '$accessLog.action',
          evidenceId: '$evidenceId',
          user: { $arrayElemAt: ['$user.username', 0] },
          ipAddress: '$accessLog.ipAddress'
        }
      }
    ]);

    // Get recent verification attempts
    const recentVerifications = await Evidence.aggregate([
      { $unwind: '$verificationAttempts' },
      { $sort: { 'verificationAttempts.timestamp': -1 } },
      { $limit: 20 },
      {
        $lookup: {
          from: 'users',
          localField: 'verificationAttempts.verifier',
          foreignField: '_id',
          as: 'verifier'
        }
      },
      {
        $project: {
          timestamp: '$verificationAttempts.timestamp',
          action: 'verify',
          evidenceId: '$evidenceId',
          user: { $arrayElemAt: ['$verifier.username', 0] },
          result: '$verificationAttempts.result'
        }
      }
    ]);

    return {
      accessLogs: recentAccess,
      verificationLogs: recentVerifications,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Real-time logs error:', error);
    return {
      accessLogs: [],
      verificationLogs: [],
      error: error.message
    };
  }
}

module.exports = router;
