// Admin Panel Management
class AdminManager {
    constructor() {
        this.currentPage = 1;
        this.currentSearch = '';
        this.currentRole = '';
        this.editingUserId = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // User search
        const searchInput = document.getElementById('user-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.currentSearch = e.target.value;
                this.loadUsers();
            });
        }

        // Role filter
        const roleFilter = document.getElementById('role-filter');
        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.currentRole = e.target.value;
                this.loadUsers();
            });
        }

        // Add user button
        const addUserBtn = document.getElementById('add-user-btn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => this.showAddUserModal());
        }

        // User form submission
        const userForm = document.getElementById('user-form');
        if (userForm) {
            userForm.addEventListener('submit', (e) => this.handleUserSubmit(e));
        }
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        // Load content based on tab
        switch (tabName) {
            case 'users':
                this.loadUsers();
                break;
            case 'stats':
                this.loadStats();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    async loadUsers() {
        try {
            showLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: 10,
                search: this.currentSearch,
                role: this.currentRole
            });

            const response = await api.request(`/admin/users?${params}`, 'GET');
            
            if (response.success) {
                this.displayUsers(response.users);
                this.displayPagination(response.pagination);
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Load users error:', error);
            showErrorNotification('Error', 'Failed to load users');
        } finally {
            hideLoading();
        }
    }

    displayUsers(users) {
        const tbody = document.getElementById('users-table-body');
        if (!tbody) return;

        tbody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <div class="user-name">${user.fullName}</div>
                            <div class="user-username">@${user.username}</div>
                            <div class="user-email">${user.email}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="role-badge role-${user.role}">${user.role.toUpperCase()}</span>
                </td>
                <td>${user.department || 'N/A'}</td>
                <td>
                    <span class="status-badge ${user.isActive ? 'active' : 'inactive'}">
                        ${user.isActive ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon" onclick="adminManager.editUser('${user._id}')" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="adminManager.resetPassword('${user._id}')" title="Reset Password">
                            <i class="fas fa-key"></i>
                        </button>
                        <button class="btn-icon ${user.isActive ? 'danger' : 'success'}" 
                                onclick="adminManager.toggleUserStatus('${user._id}', ${!user.isActive})" 
                                title="${user.isActive ? 'Deactivate' : 'Activate'}">
                            <i class="fas fa-${user.isActive ? 'ban' : 'check'}"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    displayPagination(pagination) {
        const container = document.getElementById('users-pagination');
        if (!container) return;

        const { current, pages, total } = pagination;
        
        let paginationHTML = `<div class="pagination-info">Showing ${total} users</div>`;
        
        if (pages > 1) {
            paginationHTML += '<div class="pagination-buttons">';
            
            // Previous button
            if (current > 1) {
                paginationHTML += `<button onclick="adminManager.changePage(${current - 1})">Previous</button>`;
            }
            
            // Page numbers
            for (let i = Math.max(1, current - 2); i <= Math.min(pages, current + 2); i++) {
                paginationHTML += `<button class="${i === current ? 'active' : ''}" onclick="adminManager.changePage(${i})">${i}</button>`;
            }
            
            // Next button
            if (current < pages) {
                paginationHTML += `<button onclick="adminManager.changePage(${current + 1})">Next</button>`;
            }
            
            paginationHTML += '</div>';
        }
        
        container.innerHTML = paginationHTML;
    }

    changePage(page) {
        this.currentPage = page;
        this.loadUsers();
    }

    showAddUserModal() {
        this.editingUserId = null;
        document.getElementById('user-modal-title').textContent = 'Add New User';
        document.getElementById('user-submit-text').textContent = 'Create User';
        document.getElementById('password-group').style.display = 'block';
        document.getElementById('user-form').reset();
        document.getElementById('user-modal').classList.remove('hidden');
    }

    async editUser(userId) {
        try {
            // Get user data
            const response = await api.request(`/admin/users`, 'GET');
            const user = response.users.find(u => u._id === userId);
            
            if (!user) {
                showErrorNotification('Error', 'User not found');
                return;
            }

            this.editingUserId = userId;
            document.getElementById('user-modal-title').textContent = 'Edit User';
            document.getElementById('user-submit-text').textContent = 'Update User';
            document.getElementById('password-group').style.display = 'none';
            
            // Fill form with user data
            document.getElementById('user-username').value = user.username;
            document.getElementById('user-email').value = user.email;
            document.getElementById('user-fullname').value = user.fullName;
            document.getElementById('user-role').value = user.role;
            document.getElementById('user-department').value = user.department || '';
            
            document.getElementById('user-modal').classList.remove('hidden');
        } catch (error) {
            console.error('Edit user error:', error);
            showErrorNotification('Error', 'Failed to load user data');
        }
    }

    async handleUserSubmit(event) {
        event.preventDefault();
        
        const formData = {
            username: document.getElementById('user-username').value,
            email: document.getElementById('user-email').value,
            fullName: document.getElementById('user-fullname').value,
            role: document.getElementById('user-role').value,
            department: document.getElementById('user-department').value
        };

        if (!this.editingUserId) {
            formData.password = document.getElementById('user-password').value;
        }

        try {
            showLoading();
            
            let response;
            if (this.editingUserId) {
                response = await api.request(`/admin/users/${this.editingUserId}`, 'PUT', formData);
            } else {
                response = await api.request('/admin/users', 'POST', formData);
            }
            
            if (response.success) {
                showSuccessNotification(
                    'Success', 
                    this.editingUserId ? 'User updated successfully' : 'User created successfully'
                );
                this.closeUserModal();
                this.loadUsers();
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('User submit error:', error);
            showErrorNotification('Error', 'Failed to save user');
        } finally {
            hideLoading();
        }
    }

    closeUserModal() {
        document.getElementById('user-modal').classList.add('hidden');
        this.editingUserId = null;
    }

    async toggleUserStatus(userId, activate) {
        try {
            const response = await api.request(`/admin/users/${userId}`, 'PUT', {
                isActive: activate
            });
            
            if (response.success) {
                showSuccessNotification('Success', `User ${activate ? 'activated' : 'deactivated'} successfully`);
                this.loadUsers();
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Toggle user status error:', error);
            showErrorNotification('Error', 'Failed to update user status');
        }
    }

    async resetPassword(userId) {
        const newPassword = prompt('Enter new password (minimum 6 characters):');
        if (!newPassword || newPassword.length < 6) {
            showErrorNotification('Error', 'Password must be at least 6 characters long');
            return;
        }

        try {
            const response = await api.request(`/admin/users/${userId}/reset-password`, 'POST', {
                newPassword
            });
            
            if (response.success) {
                showSuccessNotification('Success', 'Password reset successfully');
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Reset password error:', error);
            showErrorNotification('Error', 'Failed to reset password');
        }
    }

    async loadStats() {
        try {
            const response = await api.request('/admin/stats', 'GET');
            
            if (response.success) {
                this.displayStats(response.stats);
            } else {
                showErrorNotification('Error', response.message);
            }
        } catch (error) {
            console.error('Load stats error:', error);
            showErrorNotification('Error', 'Failed to load statistics');
        }
    }

    displayStats(stats) {
        // Update stat cards
        document.getElementById('total-users').textContent = stats.users.total;
        document.getElementById('total-evidence').textContent = stats.evidence.total;
        document.getElementById('verified-evidence').textContent = stats.evidence.verified;
        document.getElementById('verification-rate').textContent = stats.evidence.verificationRate + '%';

        // Display role distribution
        const roleChart = document.getElementById('role-chart');
        if (roleChart && stats.users.byRole) {
            roleChart.innerHTML = stats.users.byRole.map(role => `
                <div class="role-stat">
                    <span class="role-badge role-${role._id}">${role._id.toUpperCase()}</span>
                    <span class="role-count">${role.count}</span>
                </div>
            `).join('');
        }

        // Display recent activity
        const activityList = document.getElementById('activity-list');
        if (activityList) {
            const activities = [
                ...stats.users.recent.map(user => ({
                    type: 'user',
                    text: `New user: ${user.fullName}`,
                    date: user.createdAt
                })),
                ...stats.evidence.recent.map(evidence => ({
                    type: 'evidence',
                    text: `Evidence uploaded: ${evidence.title}`,
                    date: evidence.createdAt
                }))
            ].sort((a, b) => new Date(b.date) - new Date(a.date)).slice(0, 10);

            activityList.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <i class="fas fa-${activity.type === 'user' ? 'user-plus' : 'upload'}"></i>
                    <span>${activity.text}</span>
                    <small>${new Date(activity.date).toLocaleDateString()}</small>
                </div>
            `).join('');
        }
    }

    loadSettings() {
        // Load system settings
        console.log('Loading system settings...');
    }
}

// Global functions for modal
function closeUserModal() {
    if (window.adminManager) {
        window.adminManager.closeUserModal();
    }
}

// Create global admin manager instance
const adminManager = new AdminManager();
window.adminManager = adminManager;
