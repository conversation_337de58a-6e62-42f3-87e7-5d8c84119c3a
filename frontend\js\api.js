// API Configuration and Utilities
class API {
    constructor() {
        this.baseURL = 'http://localhost:3000/api';
        this.token = localStorage.getItem('token');
    }

    // Set authentication token
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('token', token);
        } else {
            localStorage.removeItem('token');
        }
    }

    // Get authentication headers
    getHeaders(includeAuth = true) {
        const headers = {
            'Content-Type': 'application/json'
        };

        if (includeAuth && this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        return headers;
    }

    // Generic API request method
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: this.getHeaders(options.auth !== false),
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API request error:', error);
            throw error;
        }
    }

    // Authentication endpoints
    async login(credentials) {
        return this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify(credentials),
            auth: false
        });
    }

    async createAdmin(adminData) {
        return this.request('/auth/create-admin', {
            method: 'POST',
            body: JSON.stringify(adminData),
            auth: false
        });
    }

    async getCurrentUser() {
        return this.request('/auth/me');
    }

    async updateProfile(profileData) {
        return this.request('/auth/profile', {
            method: 'PUT',
            body: JSON.stringify(profileData)
        });
    }

    async changePassword(passwordData) {
        return this.request('/auth/change-password', {
            method: 'POST',
            body: JSON.stringify(passwordData)
        });
    }

    // Evidence endpoints
    async uploadEvidence(formData) {
        const url = `${this.baseURL}/evidence/upload`;
        const config = {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`
            },
            body: formData
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('Upload evidence error:', error);
            throw error;
        }
    }

    async getEvidences(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = `/evidence${queryString ? `?${queryString}` : ''}`;
        return this.request(endpoint);
    }

    async getEvidence(id) {
        return this.request(`/evidence/${id}`);
    }

    async verifyEvidence(id) {
        return this.request(`/evidence/${id}/verify`, {
            method: 'POST'
        });
    }

    // Get unverified evidence
    async getUnverifiedEvidence() {
        return this.request('/evidence/unverified');
    }

    async getEvidenceByCase(caseId) {
        return this.request(`/evidence/case/${caseId}`);
    }

    async downloadEvidence(id) {
        const url = `${this.baseURL}/evidence/${id}/download`;
        const config = {
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            return response;
        } catch (error) {
            console.error('Download evidence error:', error);
            throw error;
        }
    }

    // System endpoints
    async getHealthStatus() {
        return this.request('/health', { auth: false });
    }

    async getBlockchainStatus() {
        return this.request('/blockchain/status', { auth: false });
    }

    // Admin endpoints
    async getBlockchainHealth() {
        return this.request('/admin/blockchain-health');
    }

    async getRealTimeLogs() {
        return this.request('/admin/real-time-logs');
    }

    async exportLegalCase(caseId) {
        const response = await fetch(`${this.baseURL}/admin/legal-export/${caseId}`, {
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        });

        if (!response.ok) {
            throw new Error('Export failed');
        }

        return response.blob();
    }
}

// Create global API instance
const api = new API();
window.api = api;
