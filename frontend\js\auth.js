// Authentication Management
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    init() {
        // Check if user is already logged in
        if (isAuthenticated()) {
            this.loadCurrentUser();
        }
        // Note: If not authenticated, the app will handle showing the home page

        // Setup login form
        this.setupLoginForm();
    }

    setupLoginForm() {
        console.log('🔧 Setting up login form...');
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            console.log('✅ Login form found, adding submit handler');
            loginForm.addEventListener('submit', (e) => {
                console.log('📝 Form submit event triggered');
                this.handleLogin(e);
            });
        } else {
            console.error('❌ Login form not found!');
        }

        // Also add click handler to the button directly as backup
        const loginButton = loginForm?.querySelector('button[type="submit"]');
        if (loginButton) {
            console.log('✅ Login button found, adding click handler');
            loginButton.addEventListener('click', (e) => {
                console.log('🔍 Login button clicked');

                // Check if button is disabled
                if (loginButton.disabled) {
                    console.log('⚠️ Login button is disabled');
                    return;
                }

                // If form submission doesn't work, handle it manually
                setTimeout(() => {
                    const form = document.getElementById('login-form');
                    if (form) {
                        console.log('🔄 Manually triggering form submission');
                        const formData = new FormData(form);
                        const credentials = {
                            username: formData.get('username'),
                            password: formData.get('password')
                        };

                        if (credentials.username && credentials.password) {
                            this.handleLogin({ preventDefault: () => {}, target: form });
                        }
                    }
                }, 100);
            });
        } else {
            console.error('❌ Login button not found!');
        }

        // Additional direct button handler by ID
        setTimeout(() => {
            const directButton = document.getElementById('login-submit-btn');
            if (directButton) {
                console.log('✅ Direct login button found by ID');
                directButton.addEventListener('click', (e) => {
                    console.log('🔍 Direct login button clicked');

                    // Ensure the form exists and has values
                    const usernameInput = document.getElementById('username');
                    const passwordInput = document.getElementById('password');

                    if (usernameInput && passwordInput) {
                        const username = usernameInput.value.trim();
                        const password = passwordInput.value.trim();

                        if (!username || !password) {
                            e.preventDefault();
                            showErrorNotification('Validation Error', 'Please enter both username and password');
                            return;
                        }

                        console.log('✅ Form validation passed, proceeding with login');
                    }
                });
            } else {
                console.error('❌ Direct login button not found by ID!');
            }
        }, 500);

        // Setup admin creation form
        const createAdminForm = document.getElementById('create-admin-form');
        if (createAdminForm) {
            createAdminForm.addEventListener('submit', (e) => this.handleCreateAdmin(e));
        }

        // Setup admin recovery buttons
        const showRecoveryBtn = document.getElementById('show-admin-recovery-btn');
        if (showRecoveryBtn) {
            showRecoveryBtn.addEventListener('click', () => this.showAdminRecoveryForm());
        }

        const cancelRecoveryBtn = document.getElementById('cancel-admin-recovery-btn');
        if (cancelRecoveryBtn) {
            cancelRecoveryBtn.addEventListener('click', () => this.hideAdminRecoveryForm());
        }
    }

    // Backup login function that can be called directly
    async performLogin() {
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');

        if (!usernameInput || !passwordInput) {
            return;
        }

        const credentials = {
            username: usernameInput.value.trim(),
            password: passwordInput.value.trim()
        };

        if (!credentials.username || !credentials.password) {
            showErrorNotification('Validation Error', 'Please enter both username and password');
            return;
        }

        try {
            showLoading();

            const response = await api.login(credentials);

            if (response.success) {
                // Store token and user data
                api.setToken(response.token);
                this.currentUser = response.user;

                showSuccessNotification(
                    'Login Successful!',
                    `Welcome back, ${response.user.fullName || response.user.username}. Role: ${response.user.role.toUpperCase()}`
                );

                // Show dashboard and update navigation
                this.showDashboard();

                // Prevent app from interfering with navigation
                if (window.app) {
                    window.app.currentPage = 'dashboard';
                    window.app.preventRedirect = true;
                }
            } else {
                showErrorNotification('Login Failed', response.message || 'Invalid credentials. Please try again.');
            }
        } catch (error) {
            console.error('Login error:', error);
            showErrorNotification('Login Error', 'Unable to connect to server. Please try again.');
        } finally {
            hideLoading();
        }
    }

    async handleLogin(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const credentials = {
            username: formData.get('username'),
            password: formData.get('password')
        };

        // Validate form
        if (!credentials.username || !credentials.password) {
            showToast('Please enter both username and password', 'error');
            return;
        }

        try {
            showLoading();
            
            const response = await api.login(credentials);
            
            if (response.success) {
                // Store token and user data
                api.setToken(response.token);
                this.currentUser = response.user;

                showSuccessNotification(
                    'Login Successful!',
                    `Welcome back, ${response.user.fullName || response.user.username}. Role: ${response.user.role.toUpperCase()}`
                );

                // Show dashboard and update navigation
                this.showDashboard();

                // Prevent app from interfering with navigation
                if (window.app) {
                    window.app.currentPage = 'dashboard';
                    // Prevent app from redirecting back to home
                    window.app.preventRedirect = true;
                }
            } else {
                showErrorNotification('Login Failed', response.message || 'Invalid credentials. Please try again.');
            }
        } catch (error) {
            console.error('Login error:', error);
            showErrorNotification('Connection Error', 'Unable to connect to server. Please check your internet connection and try again.');
        } finally {
            hideLoading();
        }
    }

    async handleCreateAdmin(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const adminData = {
            adminSetupKey: formData.get('adminSetupKey'),
            username: formData.get('username'),
            email: formData.get('email'),
            password: formData.get('password'),
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName')
        };

        // Validate form
        if (!adminData.adminSetupKey || !adminData.username || !adminData.email || !adminData.password || !adminData.firstName || !adminData.lastName) {
            showErrorNotification('Validation Error', 'Please fill in all required fields');
            return;
        }

        // Validate password strength
        if (adminData.password.length < 8) {
            showErrorNotification('Validation Error', 'Password must be at least 8 characters long');
            return;
        }

        try {
            showLoading();

            const response = await api.createAdmin(adminData);

            if (response.success) {
                showSuccessNotification('Success', 'Admin user created successfully! You can now login.');
                // Hide the admin creation form
                const adminCreationDiv = document.querySelector('.admin-creation');
                if (adminCreationDiv) {
                    adminCreationDiv.style.display = 'none';
                }
                // Clear the form
                event.target.reset();
            } else {
                showErrorNotification('Creation Failed', response.message || 'Failed to create admin user');
            }
        } catch (error) {
            console.error('Create admin error:', error);
            showErrorNotification('Connection Error', error.message || 'Failed to create admin user. Please try again.');
        } finally {
            hideLoading();
        }
    }

    showAdminRecoveryForm() {
        const recoveryForm = document.getElementById('admin-recovery-form');
        const showBtn = document.getElementById('show-admin-recovery-btn');

        if (recoveryForm && showBtn) {
            recoveryForm.style.display = 'block';
            showBtn.style.display = 'none';
        }
    }

    hideAdminRecoveryForm() {
        const recoveryForm = document.getElementById('admin-recovery-form');
        const showBtn = document.getElementById('show-admin-recovery-btn');

        if (recoveryForm && showBtn) {
            recoveryForm.style.display = 'none';
            showBtn.style.display = 'block';

            // Clear the form
            const form = document.getElementById('create-admin-form');
            if (form) {
                form.reset();
            }
        }
    }

    async loadCurrentUser() {
        try {
            const response = await api.getCurrentUser();
            
            if (response.success) {
                this.currentUser = response.user;
                this.updateUserInterface();
                this.showDashboard();
            } else {
                this.logout();
            }
        } catch (error) {
            console.error('Load user error:', error);
            this.logout();
        }
    }

    updateUserInterface() {
        if (!this.currentUser) return;

        // Update navigation
        const navbar = document.getElementById('navbar');
        const userInfo = document.getElementById('user-info');
        const userName = document.getElementById('user-name');
        const userRole = document.getElementById('user-role');

        if (navbar) navbar.style.display = 'block';
        if (userName) userName.textContent = this.currentUser.fullName || this.currentUser.username;
        if (userRole) {
            userRole.textContent = this.currentUser.role.toUpperCase();
            userRole.className = `role-badge role-${this.currentUser.role}`;
        }

        // Show/hide navigation items based on role
        this.updateNavigationByRole();
    }

    updateNavigationByRole() {
        const uploadNav = document.getElementById('nav-upload');
        const adminNav = document.getElementById('nav-admin');

        // Only police can upload evidence
        if (uploadNav) {
            uploadNav.style.display = this.currentUser.role === 'police' ? 'flex' : 'none';
        }

        // Only admin can access admin panel
        if (adminNav) {
            adminNav.style.display = this.currentUser.role === 'admin' ? 'flex' : 'none';
        }
    }

    showLoginPage() {
        this.hideAllPages();
        document.getElementById('login-page').classList.remove('hidden');

        // Show navbar but hide authenticated items
        const navbar = document.getElementById('navbar');
        navbar.style.display = 'block';

        // Show public navigation items
        document.getElementById('nav-home').style.display = 'flex';
        document.getElementById('nav-about').style.display = 'flex';
        document.getElementById('nav-login').style.display = 'flex';

        // Hide authenticated navigation items
        document.getElementById('nav-dashboard').style.display = 'none';
        document.getElementById('nav-upload').style.display = 'none';
        document.getElementById('nav-evidence').style.display = 'none';
        document.getElementById('nav-verify').style.display = 'none';
        document.getElementById('nav-admin').style.display = 'none';
        document.getElementById('nav-profile').style.display = 'none';
        document.getElementById('nav-logout').style.display = 'none';

        // Hide user info
        document.getElementById('user-info').style.display = 'none';

        // Hide admin recovery section for non-admin users
        const adminRecovery = document.getElementById('admin-recovery');
        if (adminRecovery) {
            adminRecovery.style.display = 'none';
        }
    }

    showDashboard() {
        console.log('AuthManager: Showing dashboard...');

        this.hideAllPages();

        // Show dashboard page
        const dashboardPage = document.getElementById('dashboard-page');
        if (dashboardPage) {
            dashboardPage.classList.remove('hidden');
            console.log('AuthManager: Dashboard page shown');
        } else {
            console.error('AuthManager: Dashboard page not found');
        }

        document.getElementById('navbar').style.display = 'block';

        // Hide public navigation items
        document.getElementById('nav-home').style.display = 'none';
        document.getElementById('nav-about').style.display = 'none';
        document.getElementById('nav-login').style.display = 'none';

        // Show authenticated navigation items
        document.getElementById('nav-dashboard').style.display = 'flex';
        document.getElementById('nav-upload').style.display = 'flex';
        document.getElementById('nav-evidence').style.display = 'flex';
        document.getElementById('nav-verify').style.display = 'flex';
        document.getElementById('nav-profile').style.display = 'flex';
        document.getElementById('nav-logout').style.display = 'flex';

        // Show admin panel for admin users
        const adminNav = document.getElementById('nav-admin');
        if (adminNav) {
            adminNav.style.display = this.currentUser && this.currentUser.role === 'admin' ? 'flex' : 'none';
        }

        // Show user info
        document.getElementById('user-info').style.display = 'flex';

        // Show admin recovery section for admin users only
        const adminRecovery = document.getElementById('admin-recovery');
        if (adminRecovery) {
            adminRecovery.style.display = this.currentUser && this.currentUser.role === 'admin' ? 'block' : 'none';
        }

        this.updateUserInterface();

        // Load dashboard data
        if (window.dashboardManager) {
            window.dashboardManager.loadDashboard();
        }
    }

    hideAllPages() {
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => page.classList.add('hidden'));
        
        // Remove active class from nav items
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => item.classList.remove('active'));
    }

    logout() {
        const userName = this.currentUser?.fullName || this.currentUser?.username || 'User';

        // Clear token and user data
        api.setToken(null);
        this.currentUser = null;

        // Clear any cached data
        localStorage.removeItem('user');

        // Show login page
        this.showLoginPage();

        showInfoNotification('Logged Out Successfully', `Goodbye, ${userName}. Your session has been ended securely.`);
    }

    getCurrentUser() {
        return this.currentUser;
    }

    isLoggedIn() {
        return !!this.currentUser;
    }

    hasRole(role) {
        if (!this.currentUser) return false;
        
        // Admin has access to everything
        if (this.currentUser.role === 'admin') return true;
        
        return this.currentUser.role === role;
    }

    canUploadEvidence() {
        return this.hasRole('police') || this.hasRole('admin');
    }

    canViewEvidence() {
        return this.hasRole('police') || this.hasRole('lawyer') || this.hasRole('admin');
    }

    canVerifyEvidence() {
        return this.hasRole('police') || this.hasRole('lawyer') || this.hasRole('admin');
    }

    canAccessAdminPanel() {
        return this.hasRole('admin');
    }
}

// Create global auth manager instance
const authManager = new AuthManager();
window.authManager = authManager;

// Debug: Log that authManager is available
console.log('AuthManager initialized and available globally:', !!window.authManager);

// Test function for logout (can be called from console)
window.testLogout = function() {
    console.log('Testing logout...');
    if (window.authManager) {
        console.log('AuthManager found, calling logout...');
        window.authManager.logout();
    } else {
        console.error('AuthManager not found!');
    }
};

// Test function for login (can be called from console)
window.testLogin = function(username = 'admin', password = 'admin123') {
    console.log('Testing login...');
    if (window.authManager) {
        console.log('AuthManager found, calling performLogin...');

        // Set the form values
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');

        if (usernameInput && passwordInput) {
            usernameInput.value = username;
            passwordInput.value = password;
            window.authManager.performLogin();
        } else {
            console.error('Login form inputs not found!');
        }
    } else {
        console.error('AuthManager not found!');
    }
};

// Debug function to check login form state
window.debugLoginForm = function() {
    console.log('=== LOGIN FORM DEBUG ===');
    const loginForm = document.getElementById('login-form');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginButton = document.getElementById('login-submit-btn');

    console.log('Login form found:', !!loginForm);
    console.log('Username input found:', !!usernameInput);
    console.log('Password input found:', !!passwordInput);
    console.log('Login button found:', !!loginButton);

    if (usernameInput) console.log('Username value:', usernameInput.value);
    if (passwordInput) console.log('Password value:', passwordInput.value ? '[HIDDEN]' : '[EMPTY]');
    if (loginButton) console.log('Button disabled:', loginButton.disabled);

    console.log('AuthManager available:', !!window.authManager);
    console.log('API available:', !!window.api);
    console.log('Current page:', window.app?.currentPage);
    console.log('Is authenticated:', isAuthenticated());
    console.log('========================');
};

// Manual login function that bypasses form issues
window.manualLogin = async function(username = 'admin', password = 'admin123') {
    console.log('🔧 Manual login attempt...');

    try {
        showLoading();

        const response = await fetch('http://localhost:3000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();
        console.log('Login response:', data);

        if (data.success) {
            // Store token
            localStorage.setItem('token', data.token);
            window.api.setToken(data.token);

            // Update auth manager
            if (window.authManager) {
                window.authManager.currentUser = data.user;
                window.authManager.showDashboard();
            }

            showSuccessNotification('Login Successful!', `Welcome back, ${data.user.username}`);
        } else {
            showErrorNotification('Login Failed', data.message);
        }
    } catch (error) {
        console.error('Manual login error:', error);
        showErrorNotification('Login Error', 'Unable to connect to server');
    } finally {
        hideLoading();
    }
};
