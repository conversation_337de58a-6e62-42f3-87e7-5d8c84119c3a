{"name": "evidence-protection-system", "version": "1.0.0", "description": "Blockchain-based Evidence Protection System for law enforcement and legal professionals", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "test": "jest", "compile": "truffle compile", "migrate": "truffle migrate", "ganache": "ganache-cli --deterministic --accounts 10 --host 0.0.0.0 --port 8545", "deploy": "npm run compile && npm run migrate"}, "keywords": ["blockchain", "evidence", "security", "ethereum", "solidity", "mongodb"], "author": "Evidence Protection Team", "license": "MIT", "dependencies": {"@truffle/contract": "^4.6.31", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "web3": "^4.1.1"}, "devDependencies": {"@openzeppelin/contracts": "^4.9.3", "ganache-cli": "^6.12.2", "jest": "^29.6.4", "nodemon": "^3.0.1", "truffle": "^5.11.5"}}