const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import User model
const User = require('../backend/models/User');

// Test user data
const testUsers = [
    {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        firstName: 'System',
        lastName: 'Administrator',
        department: 'IT Security',
        walletAddress: '******************************************' // Ganache account 0
    },
    {
        username: 'officer1',
        email: '<EMAIL>',
        password: 'police123',
        role: 'police',
        firstName: 'John',
        lastName: 'Smith',
        badgeNumber: 'P001',
        department: 'Criminal Investigation',
        walletAddress: '******************************************' // Ganache account 1
    },
    {
        username: 'lawyer1',
        email: '<EMAIL>',
        password: 'lawyer123',
        role: 'lawyer',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        barNumber: 'L001',
        department: 'Criminal Defense',
        walletAddress: '******************************************' // Ganache account 2
    },
    {
        username: 'officer2',
        email: '<EMAIL>',
        password: 'police123',
        role: 'police',
        firstName: 'Mike',
        lastName: 'Davis',
        badgeNumber: 'P002',
        department: 'Forensics',
        walletAddress: '******************************************' // Ganache account 3
    }
];

async function createTestUsers() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/evidence_protection', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log('Connected to MongoDB');

        // Clear existing users (optional - be careful in production!)
        await User.deleteMany({});
        console.log('Cleared existing users');

        // Create test users
        for (const userData of testUsers) {
            try {
                const user = new User(userData);
                await user.save();
                console.log(`✓ Created user: ${userData.username} (${userData.role})`);
            } catch (error) {
                console.error(`✗ Failed to create user ${userData.username}:`, error.message);
            }
        }

        console.log('\nTest users created successfully!');
        console.log('\nLogin credentials:');
        console.log('==================');
        testUsers.forEach(user => {
            console.log(`${user.role.toUpperCase()}: ${user.username} / ${user.password}`);
        });

        console.log('\nGanache Accounts:');
        console.log('=================');
        testUsers.forEach((user, index) => {
            console.log(`Account ${index}: ${user.walletAddress} (${user.username})`);
        });

    } catch (error) {
        console.error('Error creating test users:', error);
    } finally {
        await mongoose.disconnect();
        console.log('\nDisconnected from MongoDB');
    }
}

// Run the script
createTestUsers();
